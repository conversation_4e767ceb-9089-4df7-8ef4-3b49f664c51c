using System;
using System.Collections.Generic;
using System.Linq;
using Consts;
using Unity.Netcode;
using Utils.Debug;

public class InSceneNetworkBehaviour : NetworkBehaviour
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(InSceneNetworkBehaviour);

    #endregion

    public static HashSet<NetworkObject> LevelNetworkBehaviours = new();

    private static readonly HashSet<ulong> _fullySpawnedClientsIds = new(NumericConsts.MaxPlayers);

    public static bool AreAllClientsFullySpawned => _areAllClientsFullySpawned.Value;
    private static NetworkVariable<bool> _areAllClientsFullySpawned = new(false);

    private bool _isLevelBehaviour;

    private void Awake()
    {
        _isLevelBehaviour = gameObject.scene.name.Contains(StringConsts.Level, StringComparison.InvariantCultureIgnoreCase);

        if (_isLevelBehaviour)
            LevelNetworkBehaviours.Add(NetworkObject);
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        if (LevelNetworkBehaviours.All(x => x.IsSpawned))
            AllInSceneBehavioursAreSpawnedServerRPC(NetworkManager.LocalClientId);
    }

    [ServerRpc(RequireOwnership = false)]
    private void AllInSceneBehavioursAreSpawnedServerRPC(ulong clientID)
    {
        ConfirmationRoutine(_fullySpawnedClientsIds, clientID, _areAllClientsFullySpawned, "All required in-scene NetworkBehaviours spawned");
    }

    public static void ConfirmationRoutine(HashSet<ulong> clientsContainer, ulong clientID, NetworkVariable<bool> networkBool, string message)
    {
        if (clientsContainer.Contains(clientID))
            return;

        clientsContainer.Add(clientID);

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Client[{clientID}] confirmed {message}");

        if (NetworkManager.Singleton.ConnectedClients.Keys.Any(connectedClientId => !clientsContainer.Contains(connectedClientId)))
            return;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Everyone confirmed {message}");

        networkBool.Value = true;
    }

    public static void ResetConfirmationPair(HashSet<ulong> clientsContainer, NetworkVariable<bool> networkBool)
    {
        clientsContainer.Clear();
        networkBool.Value = false;
    }

    public static void ResetSpawnStatus()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Reset AllClientsFullySpawned status");

        ResetConfirmationPair(_fullySpawnedClientsIds, _areAllClientsFullySpawned);
        LevelNetworkBehaviours.Clear();
    }

    public override void OnDestroy()
    {
        if (_isLevelBehaviour)
            LevelNetworkBehaviours.Remove(NetworkObject);

        base.OnDestroy();
    }
}
